#!/usr/bin/env node

/**
 * Test with real API key generated from extension
 * Run this after generating an API key from the WebAI extension popup
 */

const http = require('http');

// Get API key from command line argument
const apiKey = process.argv[2];

if (!apiKey) {
  console.log('❌ Please provide an API key from the WebAI extension:');
  console.log('');
  console.log('1. Click the WebAI extension icon');
  console.log('2. Generate an API key');
  console.log('3. Copy the key');
  console.log('4. Run: node test-with-real-key.js YOUR_API_KEY');
  console.log('');
  console.log('Example: node test-with-real-key.js wai_abc123...');
  process.exit(1);
}

function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'WebAI-Real-Key-Test/1.0',
        ...headers
      }
    };

    console.log(`🚀 ${method} ${path}`);

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    if (data) req.write(JSON.stringify(data));
    req.end();
  });
}

async function testRealChat() {
  console.log('🔑 Testing with Real API Key');
  console.log('============================');
  console.log(`Key: ${apiKey.substring(0, 8)}...${apiKey.substring(-4)}`);
  console.log('');

  try {
    // Test chat completion with real key
    const result = await makeRequest('/v1/chat/completions', 'POST', {
      model: 'claude-web',
      messages: [
        { 
          role: 'user', 
          content: 'Write a 2 paras about dogs' 
        }
      ],
      stream: false,
      temperature: 0.1,
      max_tokens: 50
    }, {
      'Authorization': `Bearer ${apiKey}`
    });

    console.log(`📊 Status: ${result.status}`);
    
    if (result.status === 200) {
      console.log('🎉 SUCCESS! Chat completion with real API key worked!');
      console.log('');
      
      if (result.data.choices && result.data.choices[0]) {
        const response = result.data.choices[0].message?.content;
        console.log(`🤖 AI Response: "${response}"`);
        console.log(`📏 Length: ${response?.length || 0} characters`);
        console.log(`⏱️  Model: ${result.data.model || 'unknown'}`);
        console.log(`🔢 Usage: ${JSON.stringify(result.data.usage || {})}`);
        console.log(`🆔 Request ID: ${result.data.id || 'unknown'}`);
        
        if (response && response.toLowerCase().includes('webai test successful')) {
          console.log('');
          console.log('✅ Perfect! The AI model is responding correctly!');
          console.log('🎯 Your WebAI setup is fully functional and ready for Cline!');
        } else {
          console.log('');
          console.log('✅ Chat working, but response doesn\'t match expected text');
          console.log('   This might be normal - the AI might paraphrase the response');
        }
        
      } else {
        console.log('⚠️  No response content found in result');
        console.log('📋 Full response:', JSON.stringify(result.data, null, 2));
      }
      
    } else if (result.status === 401) {
      console.log('❌ API key rejected - make sure you copied the full key correctly');
      console.log('   Try generating a new key from the extension popup');
      
    } else if (result.status === 500) {
      console.log('💥 Server error occurred');
      console.log(`   Error: ${result.data.error?.message || 'Unknown error'}`);
      console.log('');
      console.log('🔧 Possible solutions:');
      console.log('   1. Make sure you\'re logged into claude.ai in your browser');
      console.log('   2. Try refreshing the claude.ai website');
      console.log('   3. Check the browser extension console for errors');
      console.log('   4. Try generating a new API key');
      
    } else {
      console.log(`❓ Unexpected status: ${result.status}`);
      console.log('📋 Response:', JSON.stringify(result.data, null, 2));
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

async function testStreaming() {
  console.log('\n🌊 Testing Streaming with Real Key');
  console.log('=================================');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'Accept': 'text/event-stream'
    }
  };

  return new Promise((resolve) => {
    const req = http.request(options, (res) => {
      console.log(`📥 Stream Status: ${res.statusCode}`);
      
      if (res.statusCode !== 200) {
        console.log('❌ Streaming failed');
        resolve();
        return;
      }
      
      let chunkCount = 0;
      let fullContent = '';
      
      console.log('🌊 Receiving streaming response...');
      
      res.on('data', chunk => {
        const data = chunk.toString();
        chunkCount++;
        
        // Parse SSE data
        const lines = data.split('\n').filter(line => line.startsWith('data: '));
        for (const line of lines) {
          const jsonStr = line.substring(6); // Remove 'data: '
          if (jsonStr === '[DONE]') {
            console.log('✅ Stream completed');
            console.log(`📊 Total chunks received: ${chunkCount}`);
            console.log(`📝 Full response: "${fullContent}"`);
            resolve();
            return;
          }
          
          try {
            const parsed = JSON.parse(jsonStr);
            if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
              const content = parsed.choices[0].delta.content;
              fullContent += content;
              console.log(`📨 Chunk ${chunkCount}: "${content}"`);
            }
          } catch (e) {
            // Ignore parse errors
          }
        }
      });
      
      res.on('end', () => {
        console.log('🔚 Stream ended');
        resolve();
      });
    });

    req.on('error', error => {
      console.error('❌ Streaming error:', error.message);
      resolve();
    });

    req.write(JSON.stringify({
      model: 'claude-web',
      messages: [{ role: 'user', content: 'write 4 paras about cats' }],
      stream: true,
      max_tokens: 30
    }));
    req.end();
  });
}

// Run tests
testRealChat().then(() => {
  setTimeout(() => {
    testStreaming().then(() => {
      console.log('\n🎯 All tests completed!');
      console.log('Your WebAI setup is working and ready for use with Cline or other OpenAI-compatible clients.');
    });
  }, 1000);
});