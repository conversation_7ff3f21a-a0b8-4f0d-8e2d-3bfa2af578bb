#!/usr/bin/env node

/**
 * Test DeepSeek specifically to debug the communication issue
 */

const http = require('http');

const apiKey = process.argv[2] || 'test-key';

console.log('🧪 Testing DeepSeek Communication');
console.log('=================================');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/v1/chat/completions',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
    'User-Agent': 'DeepSeek-Test/1.0'
  }
};

console.log('🔄 Testing DeepSeek model...');

const req = http.request(options, (res) => {
  console.log(`📥 Response Status: ${res.statusCode}`);
  
  let body = '';
  res.on('data', chunk => body += chunk);
  res.on('end', () => {
    try {
      const parsed = JSON.parse(body);
      
      if (res.statusCode === 200) {
        console.log('✅ SUCCESS! DeepSeek is working!');
        console.log(`🤖 Response: "${parsed.choices[0].message.content}"`);
      } else {
        console.log('❌ Error response:');
        console.log(JSON.stringify(parsed, null, 2));
      }
    } catch (e) {
      console.log('📋 Raw response:', body);
    }
  });
});

req.on('error', error => {
  console.error('❌ Request failed:', error.message);
});

req.write(JSON.stringify({
  model: 'deepseek-web',
  messages: [{ 
    role: 'user', 
    content: 'Hello! Please respond with "DeepSeek is working!" if you can see this.' 
  }],
  stream: false,
  max_tokens: 50
}));

req.end();