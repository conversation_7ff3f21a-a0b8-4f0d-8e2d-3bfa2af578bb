/* WebAI Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #f8f9fa;
  margin: 0;
  padding: 0;
  width: 320px;
  min-height: 400px;
}

.container {
  width: 320px;
  min-height: 400px;
  max-height: 600px;
  background: white;
  overflow: hidden;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
}

.version {
  font-size: 12px;
  opacity: 0.8;
}

/* Main Content */
.main-content {
  padding: 20px;
}

/* Sections */
.status-section,
.api-key-section,
.quick-settings,
.usage-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.status-section:last-child,
.api-key-section:last-child,
.quick-settings:last-child,
.usage-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.status-header,
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

h2 {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.status-dot.online {
  background-color: #28a745;
  box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.status-dot.offline {
  background-color: #dc3545;
}

.status-text {
  font-size: 13px;
  font-weight: 500;
}

/* Server Info */
.server-info {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #6c757d;
  font-size: 13px;
}

.value {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #495057;
  background: white;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #dee2e6;
}

/* Buttons */
.btn {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: #007bff;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-danger {
  background: #dc3545;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-icon {
  padding: 6px 8px;
  font-size: 14px;
}

.btn-link {
  background: transparent;
  color: #007bff;
  border: none;
  padding: 4px 0;
  text-decoration: underline;
}

.btn-link:hover {
  background: transparent;
  color: #0056b3;
  transform: none;
}

.copy-btn {
  background: #28a745;
  padding: 4px 6px;
  font-size: 12px;
}

.copy-btn:hover {
  background: #218838;
}

/* Server Controls */
.server-controls {
  display: flex;
  gap: 8px;
}

/* API Key Section */
.api-key-display {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.key-container {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.key-input {
  flex: 1;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  padding: 6px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
}

.key-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.key-expires {
  color: #6c757d;
  font-size: 12px;
}

.no-key-message {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px 0;
}

/* Quick Settings */
.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.setting-row:last-child {
  margin-bottom: 0;
}

.setting-row label {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
}

.setting-select,
.setting-input {
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
  background: white;
}

.setting-checkbox {
  width: 16px;
  height: 16px;
}

/* Usage Instructions */
.instruction-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.step-number {
  background: #007bff;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  font-size: 13px;
  color: #495057;
}

/* Footer */
.footer {
  background: #f8f9fa;
  padding: 12px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast {
  background: #333;
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 300px;
  font-size: 13px;
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast-success {
  background: #28a745;
}

.toast-error {
  background: #dc3545;
}

.toast-info {
  background: #17a2b8;
}

/* Responsive - removed for popup stability */

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 6px;
}

/* Hidden utility */
.hidden {
  display: none !important;
}