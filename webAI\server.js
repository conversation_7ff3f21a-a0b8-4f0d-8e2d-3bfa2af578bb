/**
 * WebAI Local HTTP Server
 * A companion Node.js server that works with the WebAI browser extension
 * to provide OpenAI-compatible API endpoints
 */
const http = require('http');
const url = require('url');
const { WebSocket, WebSocketServer } = require('ws');

class WebAIServer {
  constructor(port = 3000) {
    this.port = port;
    this.server = null;
    this.wsServer = null;
    this.extensionConnection = null;
    this.pendingRequests = new Map();
    this.requestId = 0;
  }

  start() {
    return new Promise((resolve, reject) => {
      // Create HTTP server
      this.server = http.createServer((req, res) => {
        this.handleHttpRequest(req, res);
      });

      // Create WebSocket server for extension communication
      this.wsServer = new WebSocketServer({ port: this.port + 1 });
      this.wsServer.on('connection', (ws) => {
        console.log('Extension connected');
        this.extensionConnection = ws;
        
        ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            
            // Handle heartbeat ping
            if (message.type === 'ping') {
              ws.send(JSON.stringify({ type: 'pong' }));
              return;
            }
            
            this.handleExtensionMessage(message);
          } catch (error) {
            console.error('Failed to parse extension message:', error);
          }
        });
        
        ws.on('close', () => {
          console.log('Extension disconnected');
          this.extensionConnection = null;
        });
        
        ws.on('error', (error) => {
          console.error('WebSocket error:', error);
          this.extensionConnection = null;
        });
        
        // Send welcome message
        ws.send(JSON.stringify({ type: 'welcome', message: 'Connected to WebAI server' }));
      });

      this.server.listen(this.port, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log(`WebAI Server running on http://localhost:${this.port}`);
          console.log(`WebSocket server running on ws://localhost:${this.port + 1}`);
          resolve();
        }
      });
    });
  }

  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('WebAI Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
      
      if (this.wsServer) {
        this.wsServer.close();
      }
    });
  }

  handleHttpRequest(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.setHeader('Access-Control-Max-Age', '86400');

    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
      console.log(`🔄 CORS Preflight: ${req.method} ${req.url}`);
      res.writeHead(200);
      res.end();
      return;
    }

    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    // Detailed request logging
    console.log(`\n📥 ${method} ${path}`);
    console.log(`   Headers:`, Object.keys(req.headers).map(k => `${k}: ${req.headers[k]}`).join(', '));
    console.log(`   User-Agent: ${req.headers['user-agent'] || 'Unknown'}`);
    console.log(`   Content-Type: ${req.headers['content-type'] || 'None'}`);
    console.log(`   Authorization: ${req.headers['authorization'] ? 'Bearer ***' : 'None'}`);

    // Check if extension is connected
    if (!this.extensionConnection) {
      console.log(`❌ Extension not connected`);
      this.sendError(res, 503, 'WebAI extension not connected');
      return;
    }
    console.log(`✅ Extension connected`);

    // Route requests - support both /v1/ and direct paths for compatibility
    if ((path === '/v1/health' || path === '/health') && method === 'GET') {
      this.sendJson(res, { status: 'healthy', extension_connected: true });
    } else if ((path === '/v1/models' || path === '/models') && method === 'GET') {
      this.forwardToExtension(req, res, 'GET_MODELS');
    } else if ((path === '/v1/chat/completions' || path === '/chat/completions') && method === 'POST') {
      this.handleChatCompletion(req, res);
    } else {
      this.sendError(res, 404, `Endpoint not found: ${path}. Available endpoints: /v1/health, /v1/models, /v1/chat/completions`);
    }
  }

  handleChatCompletion(req, res) {
    console.log(`💬 Processing chat completion request...`);
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const requestData = JSON.parse(body);
        const headers = this.extractHeaders(req);
        
        console.log(`   📄 Request body size: ${body.length} bytes`);
        console.log(`   🤖 Model: ${requestData.model}`);
        console.log(`   💭 Messages: ${requestData.messages?.length || 0} messages`);
        console.log(`   🌊 Stream: ${requestData.stream || false}`);
        console.log(`   🌡️ Temperature: ${requestData.temperature || 'default'}`);
        console.log(`   📏 Max tokens: ${requestData.max_tokens || 'default'}`);
        
        if (requestData.messages?.length > 0) {
          const lastMsg = requestData.messages[requestData.messages.length - 1];
          console.log(`   📝 Last message: "${lastMsg.content?.substring(0, 100)}${lastMsg.content?.length > 100 ? '...' : ''}"`);
        }
        
        // Validate request
        if (!requestData.model) {
          console.log(`❌ Missing model parameter`);
          this.sendError(res, 400, 'Model parameter is required');
          return;
        }
        
        if (!requestData.messages || !Array.isArray(requestData.messages)) {
          console.log(`❌ Invalid messages parameter`);
          this.sendError(res, 400, 'Messages parameter is required and must be an array');
          return;
        }

        console.log(`🔄 Forwarding to extension...`);
        // Forward to extension
        this.forwardToExtension(req, res, 'CHAT_COMPLETION', {
          ...requestData,
          headers
        });
      } catch (error) {
        console.log(`❌ JSON parse error: ${error.message}`);
        console.log(`   Raw body: ${body.substring(0, 200)}...`);
        this.sendError(res, 400, 'Invalid JSON in request body');
      }
    });
  }

  forwardToExtension(req, res, type, data = null) {
    const requestId = ++this.requestId;
    
    // Store pending request
    this.pendingRequests.set(requestId, { req, res });
    
    // Send to extension
    const message = {
      id: requestId,
      type,
      data,
      headers: this.extractHeaders(req)
    };
    
    this.extensionConnection.send(JSON.stringify(message));
    
    // Set timeout
    setTimeout(() => {
      if (this.pendingRequests.has(requestId)) {
        this.pendingRequests.delete(requestId);
        if (!res.headersSent) {
          this.sendError(res, 504, 'Request timeout');
        }
      }
    }, 30000);
  }

  handleExtensionMessage(message) {
    const { id, type, data, error } = message;
    
    console.log(`📨 Extension message: ${type} (ID: ${id})`);
    
    if (type === 'STREAM_START') {
      // Start streaming response
      if (!this.pendingRequests.has(id)) {
        console.log(`⚠️  No pending request found for stream start ID: ${id}`);
        return;
      }
      
      const { res } = this.pendingRequests.get(id);
      // Don't delete the request yet - keep it for subsequent chunks
      
      console.log(`🌊 Starting stream response`);
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        ...this.getCorsHeaders()
      });
      
      // Send the first chunk
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      
    } else if (type === 'STREAM_CHUNK') {
      // Continue streaming response
      if (!this.pendingRequests.has(id)) {
        console.log(`⚠️  No pending request found for stream chunk ID: ${id}`);
        return;
      }
      
      const { res } = this.pendingRequests.get(id);
      // Don't delete the request yet - keep it for subsequent chunks
      
      console.log(`📊 Stream chunk received`);
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      
    } else if (!this.pendingRequests.has(id)) {
      console.log(`⚠️  No pending request found for ID: ${id}`);
      return; // Request already handled or timed out
    } else {
      // Handle regular responses and stream completion
      const { res } = this.pendingRequests.get(id);
      this.pendingRequests.delete(id); // Now we can delete it
      
      if (error) {
        console.log(`❌ Extension error: ${error}`);
        this.sendError(res, 500, error);
      } else if (type === 'RESPONSE') {
        console.log(`✅ Extension response received`);
        if (data.stream) {
          console.log(`🌊 Completing stream response`);
          // Send final chunk and close stream
          if (data.content) {
            res.write(`data: ${JSON.stringify(data.content)}\n\n`);
          }
          if (data.done) {
            res.write('data: [DONE]\n\n');
          }
          res.end();
        } else {
          console.log(`📤 Sending JSON response`);
          if (data.choices && data.choices[0]) {
            const content = data.choices[0].message?.content;
            console.log(`   Response preview: "${content?.substring(0, 100)}${content?.length > 100 ? '...' : ''}"`);
          }
          this.sendJson(res, data);
        }
      }
    }
  }

  handleStreamResponse(res, data) {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      ...this.getCorsHeaders()
    });

    // Send the response data as SSE
    if (data.content) {
      res.write(`data: ${JSON.stringify(data.content)}\n\n`);
    }
    
    if (data.done) {
      res.write('data: [DONE]\n\n');
      res.end();
    }
  }

  extractHeaders(req) {
    const headers = {};
    for (const [key, value] of Object.entries(req.headers)) {
      headers[key.toLowerCase()] = value;
    }
    return headers;
  }

  getCorsHeaders() {
    return {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
    };
  }

  sendJson(res, data) {
    res.writeHead(200, {
      'Content-Type': 'application/json',
      ...this.getCorsHeaders()
    });
    res.end(JSON.stringify(data));
  }

  sendError(res, status, message) {
    const error = {
      error: {
        message,
        type: 'server_error',
        code: status
      }
    };
    
    res.writeHead(status, {
      'Content-Type': 'application/json',
      ...this.getCorsHeaders()
    });
    res.end(JSON.stringify(error));
  }
}

// CLI usage
if (require.main === module) {
  const port = process.argv[2] ? parseInt(process.argv[2]) : 3000;
  const server = new WebAIServer(port);
  
  server.start().then(() => {
    console.log('WebAI Server started successfully');
    console.log('Make sure the WebAI browser extension is installed and running');
  }).catch(err => {
    console.error('Failed to start server:', err);
    process.exit(1);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nShutting down...');
    server.stop().then(() => {
      process.exit(0);
    });
  });
}

module.exports = WebAIServer;