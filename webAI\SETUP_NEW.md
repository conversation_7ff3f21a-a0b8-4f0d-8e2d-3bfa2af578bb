# WebAI Setup Guide - Real HTTP Server

The WebAI extension now uses a **real HTTP server** architecture to provide proper OpenAI-compatible API endpoints that work with external tools like Cline.

## 🏗 Architecture Overview

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   Browser       │ ◄──────────────► │   Node.js       │
│   Extension     │                  │   HTTP Server   │
│                 │                  │                 │
│ ┌─────────────┐ │                  │ ┌─────────────┐ │
│ │AI Models    │ │                  │ │HTTP Server  │ │
│ │Integration  │ │                  │ │Port 3000    │ │
│ └─────────────┘ │                  │ └─────────────┘ │
│                 │                  │                 │
│ ┌─────────────┐ │                  │ ┌─────────────┐ │
│ │WebSocket    │ │                  │ │WebSocket    │ │
│ │Client       │ │                  │ │Server       │ │
│ │             │ │                  │ │Port 3001    │ │
│ └─────────────┘ │                  │ └─────────────┘ │
└─────────────────┘                  └─────────────────┘
                                              ▲
                                              │ HTTP Requests
                                              │
                                     ┌─────────────────┐
                                     │   Cline/Tools   │
                                     │   ↓             │
                                     │ http://localhost│
                                     │      :3000      │
                                     └─────────────────┘
```

## 📦 Installation & Setup

### Step 1: Install Node.js Dependencies

```bash
# Install server dependencies
npm install ws

# Or using the provided package file
cp server-package.json package.json
npm install
```

### Step 2: Start the HTTP Server

```bash
# Start the WebAI HTTP server
node server.js

# Or on a custom port
node server.js 3000
```

You should see:
```
WebAI Server running on http://localhost:3000
WebSocket server running on ws://localhost:3001
```

### Step 3: Install & Configure Extension

1. **Load the extension** in Chrome/Edge (Developer mode)
2. **Click the extension icon** to open popup
3. **Click "Start Server"** - this connects to the HTTP server
4. **Generate an API key** if needed

You should see "Online" status in the popup.

### Step 4: Test the Setup

Test the health endpoint:
```bash
curl http://localhost:3000/v1/health
```

Should return:
```json
{
  "status": "healthy",
  "extension_connected": true
}
```

## 🔧 Usage with Cline

### Configuration
1. **Provider**: OpenAI Compatible
2. **Base URL**: `http://localhost:3000`
3. **API Key**: Generated from extension
4. **Model**: Choose from:
   - `claude-web`
   - `gemini-web`
   - `bing-copilot`
   - `deepseek-web`
   - `perplexity-web`

### Test Request
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-web",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

## 🔍 Troubleshooting

### Server Won't Start
```bash
# Check if port is in use
netstat -an | grep 3000

# Kill process using port
# Windows:
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# macOS/Linux:
lsof -ti:3000 | xargs kill -9
```

### Extension Won't Connect
1. **Check server is running** (`node server.js`)
2. **Check WebSocket port** (3001) isn't blocked
3. **Reload extension** in Chrome
4. **Check browser console** for errors

### API Requests Fail
1. **Verify API key** is generated and copied correctly
2. **Check model login** - ensure you're logged into AI services
3. **Test health endpoint** first
4. **Check server logs** for errors

### Model-Specific Issues

**Claude**: Login to [claude.ai](https://claude.ai)
**Gemini**: Login to [gemini.google.com](https://gemini.google.com)
**Bing**: Login to [copilot.microsoft.com](https://copilot.microsoft.com)
**DeepSeek**: Login to [chat.deepseek.com](https://chat.deepseek.com)
**Perplexity**: Login to [perplexity.ai](https://perplexity.ai)

## 🚀 Production Setup

### Running as Service

**Windows (using pm2):**
```bash
npm install -g pm2
pm2 start server.js --name webai-server
pm2 startup
pm2 save
```

**macOS/Linux (using systemd):**
```bash
# Create service file
sudo nano /etc/systemd/system/webai.service

[Unit]
Description=WebAI HTTP Server
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/webai
ExecStart=/usr/bin/node server.js
Restart=always

[Install]
WantedBy=multi-user.target

# Enable and start
sudo systemctl enable webai
sudo systemctl start webai
```

### Auto-Start Extension
1. Enable "Auto-start server" in extension settings
2. Extension will auto-connect on browser startup

## 📝 API Reference

### Available Endpoints

**Health Check**
```http
GET /v1/health
```

**List Models**
```http
GET /v1/models
```

**Chat Completions**
```http
POST /v1/chat/completions
Content-Type: application/json
Authorization: Bearer {api_key}

{
  "model": "claude-web",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "stream": true
}
```

### Response Format

**Success Response:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "claude-web",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "Hello! How can I help you today?"
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

**Error Response:**
```json
{
  "error": {
    "message": "Invalid API key",
    "type": "server_error",
    "code": 401
  }
}
```

## 🔐 Security Notes

- **API keys** are temporary and expire after 24 hours
- **Local only** - server only accepts localhost connections
- **Session-based** - uses your existing browser sessions
- **No data storage** - conversations aren't saved on server

## 🛠 Development

### Server Development
```bash
# Watch for changes
nodemon server.js

# Debug mode
DEBUG=* node server.js
```

### Extension Development
```bash
# Build extension
npm run build

# Watch mode
npm run watch
```

### Testing
```bash
# Test health endpoint
curl http://localhost:3000/v1/health

# Test with authentication
curl -H "Authorization: Bearer your-key" \
     http://localhost:3000/v1/models
```

## 💡 Tips

1. **Keep server running** - Cline needs constant access
2. **Use API key auth** - Required for production use
3. **Monitor logs** - Check both server and extension console
4. **Update models** - Refresh AI service logins periodically
5. **Restart if issues** - Restart both server and extension if problems occur

This new architecture provides a **real HTTP server** that external tools can connect to, while the browser extension handles the AI model integration securely through your existing browser sessions.