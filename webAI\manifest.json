{"name": "WebAI - OpenAI Compatible Local Server", "description": "A browser extension that provides OpenAI-compatible API access to various Web AI models (Claude, Gemini, Bing, etc.) allowing them to be accessed locally", "manifest_version": 3, "version": "1.0.0", "author": "<PERSON><PERSON><PERSON>", "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "dist/background.bundle.js", "type": "module"}, "options_page": "options.html", "permissions": ["storage", "declarativeNetRequest", "cookies", "scripting", "tabs", "windows", "webRequest", "activeTab"], "host_permissions": ["https://*.openai.com/*", "https://chatgpt.com/*", "https://gemini.google.com/*", "https://*.claude.ai/*", "https://*.anthropic.com/*", "https://copilot.microsoft.com/*", "wss://copilot.microsoft.com/*", "https://*.microsoft.com/*", "https://*.bing.com/*", "https://*.deepseek.com/*", "https://*.perplexity.ai/*", "http://localhost:3000/*", "http://127.0.0.1:3000/*", "http://127.0.0.1:*/*"], "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["assets/sha3_wasm_bg.7b9ca65ddd.wasm", "assets/*", "lib/*"], "matches": ["<all_urls>"]}], "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}