# WebAI Troubleshooting Guide

## 🔧 Connection Issues

### Problem: "503 WebAI extension not connected"

**Cause:** The HTTP server is running but the browser extension isn't connected via WebSocket.

**Solutions:**
1. **Check extension is loaded:**
   - Go to `chrome://extensions/`
   - Ensure WebAI extension is enabled
   - Try reloading the extension

2. **Start extension connection:**
   - Click the WebAI extension icon
   - Click "Start Server" button
   - Should show "Online" status

3. **Check WebSocket connection:**
   - <PERSON> connects to `ws://localhost:3001`
   - Make sure no firewall is blocking this port

### Problem: "404 Endpoint not found"

**Cause:** Wrong URL or server not running properly.

**Solutions:**
1. **Verify server is running:**
   ```bash
   curl http://localhost:3000/v1/health
   ```
   Should return: `{"status":"healthy","extension_connected":true}`

2. **Check correct endpoints:**
   - Health: `GET /v1/health`
   - Models: `GET /v1/models`
   - Chat: `POST /v1/chat/completions`

3. **Verify Cline configuration:**
   - Provider: "OpenAI Compatible"
   - Base URL: `http://localhost:3000` (no trailing slash)
   - API Key: Generated from extension

### Problem: Extension keeps disconnecting

**Fixed!** The extension now has:
- ✅ **Auto-reconnection** - Automatically reconnects every 2 seconds
- ✅ **Heartbeat** - Keeps connection alive with 30-second pings
- ✅ **Persistent connection** - Maintains connection in background
- ✅ **Error recovery** - Handles WebSocket errors gracefully

## 🚀 Quick Setup

### 1. Start the Server
```bash
# Option 1: Quick start script
node start.js

# Option 2: Manual start
npm install ws
node server.js
```

### 2. Connect Extension
1. Click WebAI extension icon
2. Click "Start Server"
3. Generate API key
4. Verify "Online" status

### 3. Configure Cline
1. Provider: **OpenAI Compatible**
2. Base URL: **http://localhost:3000**
3. API Key: **Copy from extension**
4. Model: **claude-web** (or any available model)

## 🔍 Debug Steps

### Check Server Status
```bash
# Test health endpoint
curl http://localhost:3000/v1/health

# Expected response:
# {"status":"healthy","extension_connected":true}
```

### Check Extension Console
1. Open `chrome://extensions/`
2. Click "Details" on WebAI extension
3. Click "Inspect views service worker"
4. Check console for connection logs

### Check Available Models
```bash
curl http://localhost:3000/v1/models
```

### Test Chat Completion
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-web",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

## 📊 Connection Status

The extension now maintains a **constant connection** with:

### Auto-Reconnection Features:
- **Immediate reconnection** when connection drops
- **50 retry attempts** with 2-second intervals
- **Heartbeat monitoring** every 30 seconds
- **Background connection** always active

### Status Indicators:
- **Green dot + "Online"** = Connected and ready
- **Red dot + "Offline"** = Server or extension issue
- **Auto-retry messages** in console during reconnection

## 🐛 Common Error Messages

### "Connection timeout"
- **Cause:** Server not running on expected port
- **Solution:** Start server with `node server.js`

### "Invalid or expired API key"
- **Cause:** API key missing or wrong
- **Solution:** Generate new key in extension popup

### "Extension not connected"
- **Cause:** WebSocket connection failed
- **Solution:** Click "Start Server" in extension popup

### "Model not responding"
- **Cause:** Not logged into AI service
- **Solution:** Login to claude.ai, gemini.google.com, etc.

## 🔄 Reset Everything

If nothing works, try this complete reset:

1. **Stop server:** `Ctrl+C` in terminal
2. **Reload extension:** Chrome Extensions → Reload
3. **Clear extension data:** Chrome Extensions → Remove → Reinstall
4. **Restart server:** `node start.js`
5. **Reconnect extension:** Click "Start Server"
6. **Test health:** `curl http://localhost:3000/v1/health`

## 💡 Pro Tips

1. **Keep server running** - Don't close the terminal
2. **Use the quick start script** - `node start.js` handles everything
3. **Check extension logs** - Inspect service worker for detailed logs
4. **Monitor connection** - Extension auto-reconnects in background
5. **Test endpoints first** - Verify `/v1/health` before using Cline

## 📞 Still Having Issues?

1. **Check the logs** in both server terminal and extension console
2. **Verify ports** aren't blocked by firewall
3. **Try different port** with `node server.js 3001`
4. **Ensure AI services** are logged in (claude.ai, etc.)
5. **Check browser permissions** for the extension

The new auto-reconnection system should eliminate most connection issues!