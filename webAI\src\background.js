/**
 * Background Service Worker for WebAI
 * Handles the local API server and communication with AI models
 */
import browser from 'webextension-polyfill';

// Import the AI models bridge library
import {
  GeminiWebModel,
  BingWebModel,
  ClaudeWebModel,
  PerplexityWebModel,
  DeepseekWebModel,
  AIModelError,
  ErrorCode,
  executeTokenRetrievalLogic,
  deepseekExtractor,
  copilotExtractor
} from '../lib/ai-models-bridge.esm.js';

console.log("WebAI Background script loaded.");

// Global state
let serverPort = 3000;
let isServerRunning = false;
let apiKeys = new Map(); // Store active API keys
let modelInstances = new Map(); // Cache model instances
let requestHandlers = new Map(); // Store active request handlers
let wsConnection = null; // WebSocket connection to local server
let reconnectInterval = null; // Auto-reconnect interval
let connectionAttempts = 0; // Track reconnection attempts
let maxReconnectAttempts = 50; // Maximum reconnection attempts
let reconnectDelay = 2000; // Initial reconnect delay (2 seconds)

// Configuration
const CONFIG = {
  DEFAULT_PORT: 3000,
  MAX_CONCURRENT_REQUESTS: 10,
  API_KEY_LENGTH: 32,
  DEFAULT_EXPIRY: 24 * 60 * 60 * 1000, // 24 hours
  CORS_HEADERS: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Max-Age': '86400'
  }
};

// Available models configuration
const AVAILABLE_MODELS = {
  'claude-web': {
    name: 'Claude (Web)',
    class: ClaudeWebModel,
    supportsImages: false,
    supportsStreaming: true,
    description: 'Anthropic Claude via web interface'
  },
  'gemini-web': {
    name: 'Gemini (Web)',
    class: GeminiWebModel,
    supportsImages: true,
    supportsStreaming: true,
    description: 'Google Gemini via web interface'
  },
  'bing-copilot': {
    name: 'Bing Copilot',
    class: BingWebModel,
    supportsImages: true,
    supportsStreaming: true,
    description: 'Microsoft Bing Copilot'
  },
  'deepseek-web': {
    name: 'DeepSeek (Web)',
    class: DeepseekWebModel,
    supportsImages: true,
    supportsStreaming: true,
    description: 'DeepSeek via web interface'
  },
  'perplexity-web': {
    name: 'Perplexity (Web)',
    class: PerplexityWebModel,
    supportsImages: true,
    supportsStreaming: true,
    description: 'Perplexity via web interface'
  }
};

// Initialize background script
async function initBackground() {
  try {
    // Load settings
    const settings = await loadSettings();
    serverPort = settings.serverPort || CONFIG.DEFAULT_PORT;

    // Load existing API keys
    await loadApiKeys();

    // Auto-start server if enabled or always try to connect
    if (settings.autoStart || true) { // Always try to connect in background
      try {
        await startServer();
      } catch (error) {
        console.log('Server not available on startup, will retry automatically');
      }
    }

    console.log('WebAI background script initialized');
  } catch (error) {
    console.error('Failed to initialize background script:', error);
  }
}

// Settings management
async function loadSettings() {
  try {
    const result = await browser.storage.local.get([
      'serverPort',
      'autoStart',
      'defaultModel',
      'debugMode',
      'corsEnabled',
      'requireAuth',
      'modelSettings'
    ]);

    return {
      serverPort: result.serverPort || CONFIG.DEFAULT_PORT,
      autoStart: result.autoStart || false,
      defaultModel: result.defaultModel || 'claude-web',
      debugMode: result.debugMode || false,
      corsEnabled: result.corsEnabled !== false, // Default true
      requireAuth: result.requireAuth !== false, // Default true
      modelSettings: result.modelSettings || {}
    };
  } catch (error) {
    console.error('Failed to load settings:', error);
    return {};
  }
}

// API Key management
async function loadApiKeys() {
  try {
    const result = await browser.storage.local.get(['apiKeys']);
    const storedKeys = result.apiKeys || {};

    // Convert stored keys to Map and check expiry
    apiKeys.clear();
    const now = Date.now();

    for (const [key, data] of Object.entries(storedKeys)) {
      if (!data.expiresAt || data.expiresAt > now) {
        apiKeys.set(key, data);
      }
    }

    // Save cleaned keys back to storage
    await saveApiKeys();
  } catch (error) {
    console.error('Failed to load API keys:', error);
  }
}

async function saveApiKeys() {
  try {
    const keysObject = Object.fromEntries(apiKeys);
    await browser.storage.local.set({ apiKeys: keysObject });
  } catch (error) {
    console.error('Failed to save API keys:', error);
  }
}

function generateApiKey() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = 'wai_'; // WebAI prefix
  for (let i = 0; i < CONFIG.API_KEY_LENGTH; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

async function createApiKey(expiryMs = CONFIG.DEFAULT_EXPIRY) {
  const key = generateApiKey();
  const keyData = {
    id: key,
    createdAt: Date.now(),
    expiresAt: expiryMs ? Date.now() + expiryMs : null,
    lastUsed: null,
    usageCount: 0
  };

  apiKeys.set(key, keyData);
  await saveApiKeys();

  return keyData;
}

function validateApiKey(key) {
  if (!key) return false;

  const keyData = apiKeys.get(key);
  if (!keyData) return false;

  // Check expiry
  if (keyData.expiresAt && Date.now() > keyData.expiresAt) {
    apiKeys.delete(key);
    saveApiKeys(); // Async, but don't wait
    return false;
  }

  // Update usage
  keyData.lastUsed = Date.now();
  keyData.usageCount++;

  return true;
}

// Model management
function getModelInstance(modelId) {
  if (!AVAILABLE_MODELS[modelId]) {
    throw new AIModelError(`Unknown model: ${modelId}`, ErrorCode.INVALID_MODEL);
  }

  if (!modelInstances.has(modelId)) {
    const ModelClass = AVAILABLE_MODELS[modelId].class;
    modelInstances.set(modelId, new ModelClass());
  }

  return modelInstances.get(modelId);
}

// Server management - connects to local HTTP server with auto-reconnection
async function startServer() {
  if (isServerRunning && wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    console.log('Server connection already established');
    return;
  }

  try {
    // Start auto-reconnection if not already running
    if (!reconnectInterval) {
      startAutoReconnect();
    }

    // Connect to local WebSocket server
    await connectToLocalServer();
    isServerRunning = true;
    connectionAttempts = 0; // Reset attempts on successful connection
    console.log(`Connected to WebAI server on port ${serverPort}`);

    // Notify popup about server status
    broadcastServerStatus();
  } catch (error) {
    isServerRunning = false;
    console.error('Failed to connect to server:', error);

    // Start auto-reconnection even if initial connection fails
    if (!reconnectInterval) {
      startAutoReconnect();
    }

    throw error;
  }
}

async function stopServer() {
  try {
    // Stop auto-reconnection
    if (reconnectInterval) {
      clearInterval(reconnectInterval);
      reconnectInterval = null;
    }

    // Disconnect from WebSocket
    if (wsConnection) {
      wsConnection.close();
      wsConnection = null;
    }

    // Cancel all active requests
    for (const [requestId, handler] of requestHandlers) {
      if (handler.controller) {
        handler.controller.abort();
      }
    }
    requestHandlers.clear();

    isServerRunning = false;
    connectionAttempts = 0;
    console.log('Disconnected from WebAI server');

    // Notify popup about server status
    broadcastServerStatus();
  } catch (error) {
    console.error('Failed to disconnect from server:', error);
    throw error;
  }
}

// Auto-reconnection logic
function startAutoReconnect() {
  if (reconnectInterval) {
    return; // Already running
  }

  console.log('Starting auto-reconnection...');
  reconnectInterval = setInterval(async () => {
    // Skip if already connected
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      return;
    }

    // Skip if we've exceeded max attempts
    if (connectionAttempts >= maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      clearInterval(reconnectInterval);
      reconnectInterval = null;
      return;
    }

    connectionAttempts++;
    console.log(`Reconnection attempt ${connectionAttempts}/${maxReconnectAttempts}`);

    try {
      await connectToLocalServer();
      isServerRunning = true;
      connectionAttempts = 0; // Reset on success
      console.log('Reconnected to WebAI server');
      broadcastServerStatus();
    } catch (error) {
      console.log(`Reconnection attempt ${connectionAttempts} failed:`, error.message);
      if (isServerRunning) {
        isServerRunning = false;
        broadcastServerStatus();
      }
    }
  }, reconnectDelay);
}

// Connect to local HTTP server via WebSocket with heartbeat
function connectToLocalServer() {
  return new Promise((resolve, reject) => {
    try {
      const wsUrl = `ws://localhost:${serverPort + 1}`;

      // Close existing connection if any
      if (wsConnection) {
        wsConnection.close();
        wsConnection = null;
      }

      wsConnection = new WebSocket(wsUrl);

      wsConnection.onopen = () => {
        console.log('WebSocket connection established');

        // Start heartbeat
        startHeartbeat();

        resolve();
      };

      wsConnection.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);

          // Handle heartbeat pong
          if (message.type === 'pong') {
            // console.log('Received heartbeat pong'); // Commented to reduce noise
            return;
          }

          // Handle welcome message
          if (message.type === 'welcome') {
            console.log('Server welcome:', message.message);
            return;
          }

          handleServerMessage(message);
        } catch (error) {
          console.error('Failed to parse server message:', error);
        }
      };

      wsConnection.onclose = (event) => {
        console.log('WebSocket connection closed:', event.code, event.reason);
        wsConnection = null;

        // Stop heartbeat
        stopHeartbeat();

        if (isServerRunning) {
          console.log('Connection lost, will attempt to reconnect...');
          // Don't set isServerRunning to false immediately to avoid UI flicker
          // The auto-reconnect will handle the connection status
        }
      };

      wsConnection.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(new Error('Failed to connect to local server. Make sure the WebAI server is running.'));
      };

      // Timeout after 5 seconds
      setTimeout(() => {
        if (wsConnection && wsConnection.readyState !== WebSocket.OPEN) {
          wsConnection.close();
          reject(new Error('Connection timeout. Make sure the WebAI server is running on port ' + (serverPort + 1)));
        }
      }, 5000);

    } catch (error) {
      reject(error);
    }
  });
}

// Heartbeat to keep connection alive
let heartbeatInterval = null;

function startHeartbeat() {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
  }

  heartbeatInterval = setInterval(() => {
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      try {
        wsConnection.send(JSON.stringify({ type: 'ping' }));
      } catch (error) {
        console.error('Failed to send heartbeat:', error);
      }
    }
  }, 30000); // Send ping every 30 seconds
}

function stopHeartbeat() {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
}

// Handle messages from local server
function handleServerMessage(message) {
  const { id, type, data } = message;

  if (type === 'GET_MODELS') {
    // Send available models
    const models = Object.entries(AVAILABLE_MODELS).map(([id, config]) => ({
      id,
      object: 'model',
      name: config.name,
      description: config.description,
      capabilities: {
        supportsImages: config.supportsImages,
        supportsStreaming: config.supportsStreaming
      }
    }));

    sendToServer({
      id,
      type: 'RESPONSE',
      data: {
        object: 'list',
        data: models
      }
    });
  } else if (type === 'CHAT_COMPLETION') {
    handleChatCompletionFromServer(id, data);
  }
}

// Send message to local server
function sendToServer(message) {
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    wsConnection.send(JSON.stringify(message));
  }
}

// Handle chat completion request from server
async function handleChatCompletionFromServer(requestId, data) {
  try {
    const { model, messages, stream = false, headers, ...otherParams } = data;

    // Validate auth if required
    const settings = await loadSettings();
    if (settings.requireAuth) {
      const authHeader = headers?.['authorization'];
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        sendToServer({
          id: requestId,
          type: 'RESPONSE',
          error: 'Missing or invalid authorization header'
        });
        return;
      }

      const token = authHeader.substring(7);
      if (!validateApiKey(token)) {
        sendToServer({
          id: requestId,
          type: 'RESPONSE',
          error: 'Invalid or expired API key'
        });
        return;
      }
    }

    // Get model instance
    const modelInstance = getModelInstance(model);

    // Extract the last user message
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.role !== 'user') {
      sendToServer({
        id: requestId,
        type: 'RESPONSE',
        error: 'Last message must be from user'
      });
      return;
    }

    // Handle the request
    let fullResponse = '';
    let streamStarted = false;
    let lastSentContent = '';

    await modelInstance.sendMessage(lastMessage.content, {
      onEvent: (event) => {
        if (event.type === 'UPDATE_ANSWER') {
          fullResponse = event.data.text;

          if (stream) {
            // Calculate the delta (new content since last update)
            const deltaContent = fullResponse.substring(lastSentContent.length);

            if (deltaContent) { // Only send if there's new content
              if (!streamStarted) {
                // Send initial streaming response to start the stream
                sendToServer({
                  id: requestId,
                  type: 'STREAM_START',
                  data: {
                    id: `chatcmpl-${requestId}`,
                    object: 'chat.completion.chunk',
                    choices: [{
                      index: 0,
                      delta: { content: deltaContent },
                      finish_reason: null
                    }]
                  }
                });
                streamStarted = true;
              } else {
                // Send streaming chunk (don't use RESPONSE type to avoid deletion)
                sendToServer({
                  id: requestId,
                  type: 'STREAM_CHUNK',
                  data: {
                    id: `chatcmpl-${requestId}`,
                    object: 'chat.completion.chunk',
                    choices: [{
                      index: 0,
                      delta: { content: deltaContent },
                      finish_reason: null
                    }]
                  }
                });
              }
              lastSentContent = fullResponse;
            }
          }
        } else if (event.type === 'DONE') {
          if (stream) {
            // Send final chunk with RESPONSE type (this will close the stream)
            sendToServer({
              id: requestId,
              type: 'RESPONSE',
              data: {
                stream: true,
                content: {
                  id: `chatcmpl-${requestId}`,
                  object: 'chat.completion.chunk',
                  choices: [{
                    index: 0,
                    delta: {},
                    finish_reason: 'stop'
                  }]
                },
                done: true
              }
            });
          } else {
            // Send complete response
            sendToServer({
              id: requestId,
              type: 'RESPONSE',
              data: {
                id: `chatcmpl-${requestId}`,
                object: 'chat.completion',
                created: Math.floor(Date.now() / 1000),
                model: modelInstance.getName(),
                choices: [{
                  index: 0,
                  message: {
                    role: 'assistant',
                    content: fullResponse
                  },
                  finish_reason: 'stop'
                }],
                usage: {
                  prompt_tokens: lastMessage.content.length,
                  completion_tokens: fullResponse.length,
                  total_tokens: lastMessage.content.length + fullResponse.length
                }
              }
            });
          }
        } else if (event.type === 'ERROR') {
          sendToServer({
            id: requestId,
            type: 'RESPONSE',
            error: event.error.message
          });
        }
      }
    });

  } catch (error) {
    console.error('Chat completion error:', error);
    sendToServer({
      id: requestId,
      type: 'RESPONSE',
      error: error.message
    });
  }
}

function broadcastServerStatus() {
  browser.runtime.sendMessage({
    type: 'SERVER_STATUS_UPDATE',
    data: {
      isRunning: isServerRunning,
      port: serverPort,
      url: `http://localhost:${serverPort}`
    }
  }).catch(() => { }); // Ignore if no listeners
}

// Removed old HTTP simulation functions - now using real HTTP server

// Message handling
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('[Background] Received message:', message.type, 'from:', sender?.tab?.id || sender?.id || 'unknown');
  console.log('[Background] Message payload:', message);

  switch (message.type) {
    case 'START_SERVER':
      startServer()
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'STOP_SERVER':
      stopServer()
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'GET_SERVER_STATUS':
      sendResponse({
        success: true,
        data: {
          isRunning: isServerRunning,
          port: serverPort,
          url: `http://localhost:${serverPort}`
        }
      });
      return false;

    case 'GENERATE_API_KEY':
      createApiKey(message.expiryMs)
        .then(keyData => sendResponse({ success: true, data: keyData }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'REVOKE_API_KEY':
      apiKeys.delete(message.key);
      saveApiKeys()
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'GET_API_KEYS':
      sendResponse({
        success: true,
        data: Array.from(apiKeys.values())
      });
      return false;

    case 'API_REQUEST':
      // No longer needed - using real HTTP server
      sendResponse({ success: false, error: 'Direct API requests not supported. Use local HTTP server.' });
      return false;

    case 'GET_AUTH_TOKEN_FROM_WEBSITE':
      // Handle token extraction for models that need it
      const { serviceName, targetUrl, urlPattern, extractorName, forceNewTab } = message.payload || {};

      if (!serviceName || !targetUrl || !urlPattern || !extractorName) {
        sendResponse({ success: false, error: 'Invalid payload' });
        return false;
      }

      const extractorFunctions = {
        deepseekExtractor: deepseekExtractor,
        copilotExtractor: copilotExtractor,
      };

      const extractorFunc = extractorFunctions[extractorName];
      if (!extractorFunc) {
        sendResponse({ success: false, error: `Unknown extractor: ${extractorName}` });
        return false;
      }

      executeTokenRetrievalLogic(serviceName, targetUrl, urlPattern, extractorFunc, forceNewTab)
        .then(token => sendResponse({ success: true, token }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'PING':
      sendResponse({ success: true, message: 'PONG' });
      return false;

    case 'DEEPSEEK_INIT':
      // Handle DeepSeek initialization specifically
      console.log('[Background] DeepSeek initialization request received');
      sendResponse({ success: true, ready: true });
      return false;

    case 'BACKGROUND_READY_CHECK':
      // Confirm background script is ready for communication
      console.log('[Background] Background ready check received');
      sendResponse({ success: true, ready: true });
      return false;
      
    default:
      console.log(`[Background] Unknown message type: ${message.type}`);
      sendResponse({ success: false, error: 'Unknown message type' });
      return false;
  }
});

// Initialize when extension loads
initBackground();

console.log("WebAI background script ready.");