#!/usr/bin/env node

/**
 * Simple WebAI Chat Test
 * Usage: node test-chat.js "Your question here" [api-key] [model]
 */

const http = require('http');

const query = process.argv[2];
const apiKey = process.argv[3] || 'test-key';
const model = process.argv[4] || 'claude-web';

if (!query) {
  console.log('Usage: node test-chat.js "Your question" [api-key] [model]');
  console.log('Example: node test-chat.js "Hello!" wai_abc123 claude-web');
  process.exit(1);
}

console.log(`🤖 Asking ${model}: "${query}"`);
console.log('🌊 Streaming response:\n');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/v1/chat/completions',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
    'Accept': 'text/event-stream'
  }
};

const req = http.request(options, (res) => {
  if (res.statusCode !== 200) {
    console.log(`❌ Error: ${res.statusCode}`);
    return;
  }
  
  let fullResponse = '';
  
  res.on('data', chunk => {
    const data = chunk.toString();
    const lines = data.split('\n').filter(line => line.startsWith('data: '));
    
    for (const line of lines) {
      const jsonStr = line.substring(6);
      if (jsonStr === '[DONE]') {
        console.log('\n\n✅ Complete!');
        console.log(`📝 Full response: "${fullResponse}"`);
        return;
      }
      
      try {
        const parsed = JSON.parse(jsonStr);
        if (parsed.choices?.[0]?.delta?.content) {
          const content = parsed.choices[0].delta.content;
          process.stdout.write(content);
          fullResponse += content;
        }
      } catch (e) {
        // Ignore parse errors
      }
    }
  });
  
  res.on('end', () => {
    if (fullResponse) {
      console.log('\n\n✅ Stream ended');
    } else {
      console.log('\n❌ No response received');
    }
  });
});

req.on('error', error => {
  console.error('❌ Request failed:', error.message);
  console.log('\nMake sure:');
  console.log('1. Server is running: node server.js');
  console.log('2. Extension is connected');
  console.log('3. API key is valid');
});

req.write(JSON.stringify({
  model,
  messages: [{ role: 'user', content: query }],
  stream: true,
  max_tokens: 200
}));

req.end();