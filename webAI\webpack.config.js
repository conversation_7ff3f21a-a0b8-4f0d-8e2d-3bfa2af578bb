const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'production',
  entry: {
    background: './src/background.js',
    popup: './src/popup.js',
    options: './src/options.js',
    'api-server': './src/api-server.js'
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].bundle.js',
    clean: true
  },
  plugins: [
    new CopyPlugin({
      patterns: [
        { from: 'popup.html', to: '../popup.html' },
        { from: 'options.html', to: '../options.html' },
        { from: 'styles/', to: '../styles/' },
        { from: 'icons/', to: '../icons/', globOptions: { ignore: ['**/icon.png'] } },
        { from: 'rules.json', to: '../rules.json' }
      ],
    }),
  ],
  resolve: {
    extensions: ['.js', '.json']
  },
  optimization: {
    minimize: false // Keep readable for debugging
  }
};